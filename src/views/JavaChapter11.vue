<template>
  <div class="java-chapter11">
    <!-- 页面头部 -->
    <div class="chapter-header">
      <div class="container">
        <div class="header-content">
          <div class="chapter-info">
            <h1 class="chapter-title">第十一章：Building with <PERSON><PERSON><PERSON> and <PERSON><PERSON></h1>
            <p class="chapter-subtitle">使用 Gradle 和 Maven 进行构建</p>
            <p class="chapter-description">
              掌控项目的"中枢神经系统"：深入对比Maven和Gradle，揭示构建工具在解决实际工程问题时的不同哲学与实践
            </p>
          </div>
          <div class="progress-indicator">
            <div class="progress-circle">
              <svg class="progress-ring" width="120" height="120">
                <circle
                  class="progress-ring-circle"
                  stroke="rgba(255,255,255,0.3)"
                  stroke-width="4"
                  fill="transparent"
                  r="52"
                  cx="60"
                  cy="60"
                />
                <circle
                  class="progress-ring-circle progress-ring-circle-fill"
                  stroke="white"
                  stroke-width="4"
                  fill="transparent"
                  r="52"
                  cx="60"
                  cy="60"
                  :stroke-dasharray="`${2 * Math.PI * 52}`"
                  :stroke-dashoffset="`${2 * Math.PI * 52 * (1 - progress / 100)}`"
                />
              </svg>
              <div class="progress-text">{{ Math.round(progress) }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <div class="content-wrapper">
        <div class="content-layout">
          <!-- 侧边栏导航 -->
          <aside class="sidebar">
            <div class="outline">
              <h3>📚 章节大纲</h3>
              <div class="outline-grid">
                <div
                  v-for="(topic, index) in courseTopics"
                  :key="index"
                  @click="scrollToTopic(index)"
                  :class="['outline-item', { active: currentTopic === index }]"
                >
                  <div class="outline-number">{{ index + 1 }}</div>
                  <div class="outline-content">
                    <h4>{{ topic.title }}</h4>
                    <p>{{ topic.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
              <button @click="toggleNotes" class="tool-button">📝 笔记</button>
              <button @click="showQuiz" class="tool-button">🧠 测验</button>
            </div>
          </aside>

          <!-- 主内容区 -->
          <main class="main-content">
            <!-- Topic 1: 构建工具的必要性 -->
            <section id="topic-0" class="topic-section" ref="topic0">
              <ExpandableSection
                title="构建工具的必要性 (The Necessity of Build Tools)"
                :concept-data="buildToolsNecessityData"
                @interaction="handleInteraction"
              >
                <div class="build-tools-necessity-showcase">
                  <h3>🎯 构建工具：项目的"中枢神经系统"</h3>
                  <p class="intro-text">
                    构建工具是用于将源代码转换成可执行软件，并自动化该过程中相关任务（如编译、测试、打包、部署）的程序。
                  </p>

                  <div class="human-explanation">
                    <h4>💡 人话版解释</h4>
                    <p class="explanation-text">
                      构建工具就是一个<strong>"全自动工程管家"</strong>。你把原材料（源代码）给它，它就能按照一套标准流程，
                      自动帮你盖好房子（可执行程序），并且还能顺便把装修（打包）、质检（测试）、甚至物流（部署）都给办了。
                    </p>
                  </div>

                  <div class="core-problems">
                    <h4>🚨 现代软件开发的三大根本问题</h4>
                    <div class="problems-grid">
                      <div class="problem-card">
                        <div class="problem-header">
                          <span class="problem-icon">🔥</span>
                          <h5>依赖地狱 (Dependency Hell)</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            一个项目可能依赖几十上百个第三方库（<code>.jar</code>包），这些库之间又互相依赖。
                            手动下载和管理这些jar包的版本，并处理它们之间的冲突，是一场噩梦。
                          </p>
                          <div class="example">
                            <strong>例子：</strong>项目A依赖guava 30.0，项目B依赖guava
                            28.0，当它们合并时会产生版本冲突。
                          </div>
                        </div>
                      </div>

                      <div class="problem-card">
                        <div class="problem-header">
                          <span class="problem-icon">🔄</span>
                          <h5>重复且易错的工作流</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            编译、运行单元测试、打包、生成文档等，这些都是每次代码变更后都需要重复执行的步骤。
                            手动执行不仅效率低下，而且极易出错（例如，忘记编译某个文件）。
                          </p>
                          <div class="example">
                            <strong>例子：</strong
                            >开发者忘记运行测试就提交代码，导致CI/CD流水线失败。
                          </div>
                        </div>
                      </div>

                      <div class="problem-card">
                        <div class="problem-header">
                          <span class="problem-icon">⚠️</span>
                          <h5>环境不一致</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            开发者A的电脑上能成功编译，但在开发者B的电脑上或者在服务器上就失败了。
                            这通常是因为JDK版本、依赖库版本、环境变量等不一致造成的。
                          </p>
                          <div class="example">
                            <strong>例子：</strong>"在我机器上能跑"成为开发团队的经典问题。
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="core-principles">
                    <h4>🏗️ 构建工具的核心原理</h4>
                    <div class="principles-grid">
                      <div class="principle-card">
                        <div class="principle-header">
                          <span class="principle-icon">📋</span>
                          <h5>声明式配置</h5>
                        </div>
                        <p>
                          你只需要在配置文件中"声明"你的直接依赖及其版本，构建工具会自动分析整个依赖树，
                          下载所有需要的传递性依赖，并提供一套机制来解决版本冲突。
                        </p>
                      </div>

                      <div class="principle-card">
                        <div class="principle-header">
                          <span class="principle-icon">🤖</span>
                          <h5>工作流自动化</h5>
                        </div>
                        <p>
                          你通过定义任务（Task）或生命周期（Lifecycle）来"声明"你的构建流程，
                          然后只需一个命令，工具就会自动执行所有步骤。
                        </p>
                      </div>

                      <div class="principle-card">
                        <div class="principle-header">
                          <span class="principle-icon">🔒</span>
                          <h5>一致性保证</h5>
                        </div>
                        <p>
                          构建工具本身（通过其wrapper）和它的配置文件是项目的一部分，随代码一起提交。
                          这保证了任何人在任何机器上，只要使用同一个构建命令，就能得到完全一致的构建环境和结果。
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="build-tools-mindmap">
                    <h4>🗺️ 构建工具知识脉络图</h4>
                    <div class="mindmap-container">
                      <div id="build-tools-mindmap" class="mermaid-container">
                        <div class="mindmap-placeholder">
                          <p>🎨 正在生成思维导图...</p>
                          <p>如果长时间未显示，请刷新页面</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="wrapper-solution">
                    <h4>🔧 Wrapper机制：一劳永逸的环境一致性解决方案</h4>
                    <div class="wrapper-explanation">
                      <p>
                        <strong>Maven Wrapper (mvnw) / Gradle Wrapper (gradlew)</strong>：
                        这两个都是官方提供的工具。它会在项目中生成几个脚本文件和一个小的<code>.jar</code>包。
                      </p>
                    </div>
                    <div class="wrapper-mechanism">
                      <h5>工作机制</h5>
                      <div class="mechanism-steps">
                        <div class="step">
                          <span class="step-number">1</span>
                          <p>
                            当你执行 <code>./gradlew build</code> 或 <code>./mvnw package</code> 时
                          </p>
                        </div>
                        <div class="step">
                          <span class="step-number">2</span>
                          <p>脚本会先检查你本地是否已经下载了项目指定的、确切版本的构建工具</p>
                        </div>
                        <div class="step">
                          <span class="step-number">3</span>
                          <p>
                            如果没有，它会<strong>自动下载</strong>这个版本到项目的一个本地目录中
                          </p>
                        </div>
                        <div class="step">
                          <span class="step-number">4</span>
                          <p>然后再用这个下载好的、版本正确的工具来执行构建</p>
                        </div>
                      </div>
                    </div>
                    <div class="wrapper-benefits">
                      <h5>核心优势</h5>
                      <div class="benefits-grid">
                        <div class="benefit-item">
                          <span class="benefit-icon">✅</span>
                          <div>
                            <h6>版本一致性</h6>
                            <p>保证所有开发者使用相同版本的构建工具</p>
                          </div>
                        </div>
                        <div class="benefit-item">
                          <span class="benefit-icon">🚀</span>
                          <div>
                            <h6>零配置上手</h6>
                            <p>新成员无需安装任何构建工具即可开始工作</p>
                          </div>
                        </div>
                        <div class="benefit-item">
                          <span class="benefit-icon">🔒</span>
                          <div>
                            <h6>构建可复现</h6>
                            <p>任何机器上都能得到完全一致的构建结果</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 2: Maven：约定优于配置 -->
            <section id="topic-1" class="topic-section" ref="topic1">
              <ExpandableSection
                title="Maven：约定优于配置 (Maven: Convention over Configuration)"
                :concept-data="mavenConventionData"
                @interaction="handleInteraction"
              >
                <div class="maven-showcase">
                  <h3>👑 Maven：构建工具界的"霸道总裁"</h3>
                  <p class="intro-text">
                    Maven 是一个强大的项目管理和构建自动化工具。它的核心哲学是<strong
                      >"约定优于配置" (Convention over Configuration)</strong
                    >。
                  </p>

                  <div class="human-explanation">
                    <h4>💡 人话版解释</h4>
                    <p class="explanation-text">
                      Maven 就像一个<strong>"霸道总裁"</strong>。它为你规定好了一切："代码必须放在
                      <code>src/main/java</code>"， "测试必须放在
                      <code>src/test/java</code>"，"构建产物必须输出到 <code>target</code> 目录"，
                      "构建必须先<code>compile</code>再<code>test</code>然后<code>package</code>"。
                      你只要遵守它的这些"约定"，就可以用极少的配置来完成工作。
                    </p>
                  </div>

                  <div class="maven-core-concepts">
                    <h4>🎯 Maven 三大核心概念</h4>
                    <div class="concepts-grid">
                      <div class="concept-card">
                        <div class="concept-header">
                          <span class="concept-icon">📄</span>
                          <h5>项目对象模型 (POM)</h5>
                        </div>
                        <div class="concept-content">
                          <p>
                            所有配置都集中在一个
                            <code>pom.xml</code>
                            文件中。这个文件是对项目元数据（项目名、依赖、插件等）的声明式描述。
                          </p>
                          <div class="code-example">
                            <pre><code>&lt;project&gt;
  &lt;groupId&gt;com.example&lt;/groupId&gt;
  &lt;artifactId&gt;my-app&lt;/artifactId&gt;
  &lt;version&gt;1.0.0&lt;/version&gt;
  &lt;packaging&gt;jar&lt;/packaging&gt;
&lt;/project&gt;</code></pre>
                          </div>
                        </div>
                      </div>

                      <div class="concept-card">
                        <div class="concept-header">
                          <span class="concept-icon">📁</span>
                          <h5>标准目录结构</h5>
                        </div>
                        <div class="concept-content">
                          <p>强制或推荐一套标准的目录布局，确保所有Maven项目都有相同的结构。</p>
                          <div class="directory-structure">
                            <div class="dir-item">📁 src/main/java - 主要源代码</div>
                            <div class="dir-item">📁 src/test/java - 测试代码</div>
                            <div class="dir-item">📁 src/main/resources - 资源文件</div>
                            <div class="dir-item">📁 target - 构建输出</div>
                          </div>
                        </div>
                      </div>

                      <div class="concept-card">
                        <div class="concept-header">
                          <span class="concept-icon">🔄</span>
                          <h5>构建生命周期</h5>
                        </div>
                        <div class="concept-content">
                          <p>
                            定义了一系列有序的、抽象的构建阶段（Phase），如 validate, compile, test,
                            package, install, deploy。
                          </p>
                          <div class="lifecycle-flow">
                            <div class="phase">validate</div>
                            <div class="arrow">→</div>
                            <div class="phase">compile</div>
                            <div class="arrow">→</div>
                            <div class="phase">test</div>
                            <div class="arrow">→</div>
                            <div class="phase">package</div>
                            <div class="arrow">→</div>
                            <div class="phase">install</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="maven-analogy">
                    <h4>🎨 生动类比：Maven的生命周期</h4>
                    <div class="analogy-content">
                      <p>Maven的生命周期就像<strong>宜家家具的安装说明书</strong>：</p>
                      <ul class="analogy-list">
                        <li>
                          <strong>生命周期</strong
                          >：说明书上印好了固定的步骤1、2、3、4（<code>compile</code>,
                          <code>test</code>, <code>package</code>...）
                        </li>
                        <li>
                          <strong>插件目标</strong
                          >：每个步骤具体要干什么（比如步骤1是"用A号螺丝拧紧B号板"），是由具体的"插件"决定的
                        </li>
                        <li>
                          <strong>pom.xml</strong
                          >：就是你的零件清单和一些个性化需求（比如"我不想用标配的把手，想换个铜的"）
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div class="maven-lifecycle-demo">
                    <h4>🔄 Maven生命周期实例演示</h4>
                    <div class="lifecycle-explanation">
                      <p>
                        当你在命令行执行 <code>mvn package</code> 时，Maven
                        并不是只执行"打包"这一个动作。
                        它会严格按照生命周期，<strong>从头开始</strong>依次执行所有在
                        <code>package</code> 之前的阶段。
                      </p>
                    </div>
                    <div class="lifecycle-demo">
                      <div class="lifecycle-step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                          <h6>validate</h6>
                          <p>验证项目结构和配置</p>
                        </div>
                      </div>
                      <div class="step-arrow">↓</div>
                      <div class="lifecycle-step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                          <h6>compile</h6>
                          <p>编译主要源代码</p>
                        </div>
                      </div>
                      <div class="step-arrow">↓</div>
                      <div class="lifecycle-step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                          <h6>test</h6>
                          <p>运行单元测试</p>
                        </div>
                      </div>
                      <div class="step-arrow">↓</div>
                      <div class="lifecycle-step active">
                        <div class="step-number">4</div>
                        <div class="step-content">
                          <h6>package</h6>
                          <p>打包成JAR/WAR文件</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="maven-pitfalls">
                    <h4>⚠️ 常见理论误区与注意事项</h4>
                    <div class="pitfalls-grid">
                      <div class="pitfall-card">
                        <div class="pitfall-header">
                          <span class="pitfall-icon">❌</span>
                          <h5>误区</h5>
                        </div>
                        <p>
                          认为执行 <code>mvn test</code> 就只会运行测试。实际上，它会先执行
                          <code>test</code> 之前的所有阶段，包括 <code>compile</code>。
                        </p>
                      </div>
                      <div class="pitfall-card">
                        <div class="pitfall-header">
                          <span class="pitfall-icon">⚠️</span>
                          <h5>注意</h5>
                        </div>
                        <p>
                          XML 的冗长是 Maven 最大的痛点之一。对于复杂的构建逻辑，<code
                            >pom.xml</code
                          >
                          会变得非常庞大且难以阅读。
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="real-world-problems">
                    <h3>🚨 项目实践踩坑与解决方案</h3>
                    <div class="problem-solution-card">
                      <div class="problem-section">
                        <h4>常见问题描述</h4>
                        <p>
                          在一个大型多模块（Multi-module）Maven 项目中，不同的子模块（如
                          <code>module-a</code>, <code>module-b</code>） 都依赖了
                          <code>guava</code> 和 <code>httpclient</code> 这两个库，但它们在各自的
                          <code>pom.xml</code> 中指定的版本不一致。
                          这导致了整个项目依赖混乱，构建结果不可预测，并且在运行时频繁出现
                          <code>NoSuchMethodError</code>。
                        </p>
                      </div>

                      <div class="root-cause-section">
                        <h4>问题根源分析</h4>
                        <p>
                          <strong>缺乏统一的依赖版本管理</strong
                          >。在多模块项目中，如果每个子模块都自己定义依赖版本，就会产生版本冲突。
                          Maven 的"就近原则"(nearest wins)
                          解决冲突的策略，会因为模块依赖关系的变化而产生不同的仲裁结果，使得构建非常脆弱。
                        </p>
                      </div>

                      <div class="solution-section">
                        <h4>业界主流解决方案与权衡</h4>
                        <div class="solution-demo">
                          <h5>方案：使用 &lt;dependencyManagement&gt; 标签</h5>
                          <ol class="solution-steps">
                            <li>
                              在<strong>父 POM</strong> 中，使用
                              <code>&lt;dependencyManagement&gt;</code>
                              标签来<strong>声明</strong>整个项目所有可能用到的依赖及其<strong>统一的版本号</strong>。
                            </li>
                            <li>
                              在各个<strong>子模块</strong>的
                              <code>pom.xml</code> 中，当需要使用某个依赖时，只需要提供
                              <code>groupId</code> 和 <code>artifactId</code>，<strong
                                >不需要也不能指定 version</strong
                              >。
                            </li>
                          </ol>

                          <div class="code-examples">
                            <div class="code-example-section">
                              <h6>父 POM 配置</h6>
                              <div class="code-example">
                                <pre><code>&lt;dependencyManagement&gt;
  &lt;dependencies&gt;
    &lt;dependency&gt;
      &lt;groupId&gt;com.google.guava&lt;/groupId&gt;
      &lt;artifactId&gt;guava&lt;/artifactId&gt;
      &lt;version&gt;31.1-jre&lt;/version&gt;
    &lt;/dependency&gt;
    &lt;dependency&gt;
      &lt;groupId&gt;org.apache.httpcomponents&lt;/groupId&gt;
      &lt;artifactId&gt;httpclient&lt;/artifactId&gt;
      &lt;version&gt;4.5.13&lt;/version&gt;
    &lt;/dependency&gt;
  &lt;/dependencies&gt;
&lt;/dependencyManagement&gt;</code></pre>
                              </div>
                            </div>

                            <div class="code-example-section">
                              <h6>子模块配置</h6>
                              <div class="code-example">
                                <pre><code>&lt;dependencies&gt;
  &lt;dependency&gt;
    &lt;groupId&gt;com.google.guava&lt;/groupId&gt;
    &lt;artifactId&gt;guava&lt;/artifactId&gt;
    &lt;!-- 版本号自动从父POM继承 --&gt;
  &lt;/dependency&gt;
  &lt;dependency&gt;
    &lt;groupId&gt;org.apache.httpcomponents&lt;/groupId&gt;
    &lt;artifactId&gt;httpclient&lt;/artifactId&gt;
    &lt;!-- 版本号自动从父POM继承 --&gt;
  &lt;/dependency&gt;
&lt;/dependencies&gt;</code></pre>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="tradeoffs">
                          <h5>利弊与权衡</h5>
                          <div class="tradeoffs-grid">
                            <div class="pros">
                              <h6>✅ 优点</h6>
                              <ul>
                                <li>
                                  <strong>集中管理了所有依赖的版本</strong
                                  >，保证了整个项目的版本一致性
                                </li>
                                <li>子模块的 POM 变得更干净</li>
                                <li>升级依赖时，只需要修改父 POM 的一处地方即可</li>
                                <li>这套机制也被称为 <strong>BOM (Bill of Materials)</strong></li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 缺点</h6>
                              <ul>
                                <li>需要一个良好的父子模块结构设计</li>
                                <li>增加了项目结构的复杂性</li>
                              </ul>
                            </div>
                          </div>
                          <div class="industry-choice">
                            <p>
                              <strong>业界选择：</strong>在任何多模块 Maven 项目中，<strong
                                >使用 <code>&lt;dependencyManagement&gt;</code> 和 BOM
                                是管理依赖版本的标准最佳实践</strong
                              >。 几乎所有的开源框架（如 Spring Boot, Spring Cloud）都提供自己的 BOM
                              文件，供用户方便地导入和管理其庞大的依赖体系。
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 3: Gradle：灵活性与性能 -->
            <section id="topic-2" class="topic-section" ref="topic2">
              <ExpandableSection
                title="Gradle：灵活性与性能 (Gradle: Flexibility and Performance)"
                :concept-data="gradleFlexibilityData"
                @interaction="handleInteraction"
              >
                <div class="gradle-showcase">
                  <h3>⚡ Gradle：构建工具界的"创客工具箱"</h3>
                  <p class="intro-text">
                    Gradle 是一个先进的构建自动化工具，它吸取了 Maven
                    的优点（如依赖管理、标准目录结构）， 但用一种基于<strong
                      >领域特定语言 (DSL)</strong
                    >
                    的、可编程的构建脚本 (<code>build.gradle.kts</code> 或
                    <code>build.gradle</code>) 替代了 Maven 僵化的 XML。
                  </p>

                  <div class="human-explanation">
                    <h4>💡 人话版解释</h4>
                    <p class="explanation-text">
                      如果说 Maven 是一个只能通过复杂配置文件来操作的<strong>"黑盒设备"</strong>，
                      那么 Gradle 就是一个提供了强大
                      <strong>API</strong> 的<strong>"可编程设备"</strong>。 你可以用代码（Kotlin 或
                      Groovy）来精确地、灵活地控制构建过程的每一个细节。
                    </p>
                  </div>

                  <div class="gradle-principles">
                    <h4>🎯 深度解读与"第一性原理"追问</h4>
                    <div class="principles-grid">
                      <div class="principle-card">
                        <h5>🎯 存在价值</h5>
                        <p>为了解决 Maven 的两大核心痛点：</p>
                        <ul>
                          <li>
                            <strong>僵化 (Rigidity)</strong>：Maven
                            固定的生命周期很难适应非标准的构建需求
                          </li>
                          <li>
                            <strong>性能 (Performance)</strong>：Maven 缺乏精细的增量构建和缓存机制
                          </li>
                        </ul>
                      </div>
                      <div class="principle-card">
                        <h5>⚙️ 核心原理</h5>
                        <ul>
                          <li>
                            <strong>代码即配置</strong>：构建脚本本身就是可执行代码，赋予无限灵活性
                          </li>
                          <li>
                            <strong>任务图 (Task Graph)</strong>：基于有向无环图的任务依赖关系
                          </li>
                          <li>
                            <strong>增量构建与缓存</strong>：智能分析输入/输出，跳过未改变的任务
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div class="gradle-example">
                    <h4>📝 实例与类比</h4>
                    <div class="code-example">
                      <h5>简单的 build.gradle.kts 配置文件</h5>
                      <pre><code>plugins {
    `java-library` // 应用java库插件
}
dependencies {
    // 声明一个编译时依赖
    implementation("com.google.guava:guava:31.1-jre")
    // 声明一个只在测试时需要的依赖
    testImplementation("org.junit.jupiter:junit-jupiter-api:5.8.1")
}</code></pre>
                      <p class="code-note">
                        这段代码远比等效的
                        <code>pom.xml</code> 简洁，并且由于它是代码，可以轻松地加入自定义逻辑。
                      </p>
                    </div>
                    <div class="analogy">
                      <h5>🔧 生动类比</h5>
                      <p>
                        如果说 Maven
                        是<strong>乐高玩具</strong>，你只能用固定的模块按照说明书来拼装； 那么
                        Gradle
                        就是<strong>一整套的创客工具箱</strong>，里面有3D打印机、激光切割机和可编程的树莓派。
                        你可以创造出任何你想要的形状和逻辑，自由度极高。
                      </p>
                    </div>
                  </div>

                  <div class="gradle-comparison">
                    <h4>🔄 横向对比与关联</h4>
                    <div class="comparison-table">
                      <table>
                        <thead>
                          <tr>
                            <th>特性</th>
                            <th>Maven</th>
                            <th>Gradle</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td><strong>配置文件</strong></td>
                            <td>XML (声明式)</td>
                            <td>Kotlin/Groovy DSL (可编程)</td>
                          </tr>
                          <tr>
                            <td><strong>核心模型</strong></td>
                            <td>固定的生命周期</td>
                            <td>灵活的任务图 (DAG)</td>
                          </tr>
                          <tr>
                            <td><strong>灵活性</strong></td>
                            <td>差，难于定制</td>
                            <td>极高，易于编写自定义逻辑</td>
                          </tr>
                          <tr>
                            <td><strong>性能</strong></td>
                            <td>一般，无内置缓存</td>
                            <td>非常高（增量构建、构建缓存）</td>
                          </tr>
                          <tr>
                            <td><strong>学习曲线</strong></td>
                            <td>较低，概念简单</td>
                            <td>较高，需要理解其DSL和任务模型</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <div class="gradle-pitfalls">
                    <h4>⚠️ 常见理论误区与注意事项</h4>
                    <div class="pitfall-card">
                      <h5>❌ 误区</h5>
                      <p>
                        认为 Gradle
                        只是"另一个构建工具"。它的核心优势在于其<strong>性能</strong>和<strong>灵活性</strong>，
                        特别是在大型、复杂或有特殊构建需求的项目中，其优势会呈压倒性。
                      </p>
                    </div>
                    <div class="pitfall-card">
                      <h5>⚠️ 注意</h5>
                      <p>
                        Gradle
                        的灵活性是一把双刃剑。如果缺乏纪律和好的设计，构建脚本本身可能变成一个复杂的、
                        难以维护的"程序中的程序"。
                      </p>
                    </div>
                  </div>

                  <div class="gradle-real-world">
                    <h4>🏭 项目实践踩坑与解决方案</h4>
                    <div class="problem-solution">
                      <div class="problem-description">
                        <h5>🚨 常见问题描述</h5>
                        <p>
                          一个大型微服务项目包含几十个模块，使用 Maven 构建。每次 CI/CD
                          流水线运行时，
                          即使只改动了一个模块的一行代码，整个构建过程（包括所有模块的编译、测试）也需要耗费15-20分钟，
                          开发团队的反馈循环极慢，严重影响效率。
                        </p>
                      </div>
                      <div class="root-cause">
                        <h5>🔍 问题根源分析</h5>
                        <p>
                          <strong>缺乏智能的变更检测和任务缓存机制</strong
                          >。传统的构建模式无法精确地识别出哪些模块受到了改动的影响，
                          因此只能采用最安全但最低效的"全量构建"模式。
                        </p>
                      </div>
                      <div class="solutions">
                        <h5>🛠️ 业界主流解决方案与权衡</h5>
                        <div class="solution-item">
                          <h6>方案：迁移到 Gradle 并启用其性能优化特性</h6>
                          <div class="solution-details">
                            <div class="feature">
                              <strong>1. 增量构建 (Incremental Build)</strong>
                              <p>
                                这是 Gradle 的默认行为。Gradle
                                会计算每个任务的输入（如源文件、依赖版本）的哈希值。
                                如果本次构建时，一个任务的输入哈希值与上次成功构建时完全相同，
                                Gradle 会认为该任务是 <code>UP-TO-DATE</code> 并直接跳过执行。
                              </p>
                            </div>
                            <div class="feature">
                              <strong>2. 构建缓存 (Build Cache)</strong>
                              <p>
                                更进一步，你可以启用构建缓存 (<code>--build-cache</code>)。 Gradle
                                会将每个任务的输出与它的输入哈希值一起存入一个缓存。
                                当一个任务需要执行时，Gradle 会先用其输入哈希值去缓存里查找。
                                如果命中，它会直接从缓存中拉取输出结果，而无需执行任务本身。
                                这个缓存可以是本地的，也可以是团队共享的<strong>远程缓存</strong>。
                              </p>
                            </div>
                          </div>
                        </div>
                        <div class="pros-cons">
                          <div class="pros">
                            <h6>✅ 优点</h6>
                            <ul>
                              <li><strong>构建速度得到数量级的提升</strong></li>
                              <li>
                                CI服务器构建分支并填充远程缓存后，开发者本地构建时间可能从20分钟缩短到1分钟以内
                              </li>
                            </ul>
                          </div>
                          <div class="cons">
                            <h6>❌ 缺点</h6>
                            <ul>
                              <li>配置和维护一个共享的远程构建缓存需要额外的基础设施和管理成本</li>
                            </ul>
                          </div>
                        </div>
                        <div class="industry-choice">
                          <p>
                            <strong>业界选择</strong
                            >：对于任何追求极致开发效率和快速反馈的现代化工程团队，
                            <strong>充分利用 Gradle 的增量构建和构建缓存是核心实践</strong>。
                            这带来的生产力提升，远超过其基础设施的成本。 这是 Gradle 相对 Maven
                            最具吸引力的特性之一。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 4: 实战对比：依赖冲突处理 -->
            <section id="topic-3" class="topic-section" ref="topic3">
              <ExpandableSection
                title="实战对比：依赖冲突处理 (Practical Comparison: Dependency Conflict Resolution)"
                :concept-data="dependencyConflictData"
                @interaction="handleInteraction"
              >
                <div class="dependency-conflict-showcase">
                  <h3>⚔️ 依赖冲突：构建工具的"终极考验"</h3>
                  <p class="intro-text">
                    依赖冲突是指在一个项目的依赖树中，对于同一个库（如 <code>guava</code>），
                    存在多个不同的版本要求。构建工具必须选择一个最终版本来使用，这个选择过程就叫<strong>冲突解决</strong>。
                  </p>

                  <div class="human-explanation">
                    <h4>💡 人话版解释</h4>
                    <p class="explanation-text">
                      你的项目依赖了A和B两个库。A说："我需要1.0版的C"。B说："我需要2.0版的C"。
                      你的项目最终的 classpath
                      里，C只能有一个版本，到底是1.0还是2.0？这就是依赖冲突。
                    </p>
                  </div>

                  <div class="conflict-principles">
                    <h4>🎯 深度解读与"第一性原理"追问</h4>
                    <div class="principles-grid">
                      <div class="principle-card">
                        <h5>🎯 存在价值</h5>
                        <p>
                          这是传递性依赖不可避免的副产品。在一个庞大的依赖网络中，冲突是常态。
                          构建工具必须有一套明确的、可预测的规则来处理这种冲突，否则构建将是完全不确定的。
                        </p>
                      </div>
                      <div class="principle-card">
                        <h5>⚙️ 核心原理</h5>
                        <p>
                          Maven 和 Gradle
                          采用了不同的默认策略，这反映了它们在"易用性"和"正确性"之间的不同权衡。
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="conflict-example">
                    <h4>📝 实例与类比</h4>
                    <div class="example-scenario">
                      <h5>具象案例：项目依赖树</h5>
                      <div class="dependency-tree">
                        <ul>
                          <li>
                            <code>MyProject</code> → <code>LibA v1.0</code> →
                            <code>Guava v28.0</code>
                          </li>
                          <li>
                            <code>MyProject</code> → <code>LibB v2.0</code> →
                            <code>Guava v30.0</code>
                          </li>
                        </ul>
                      </div>
                      <div class="resolution-strategies">
                        <div class="strategy">
                          <h6>Maven ("就近原则")</h6>
                          <p>
                            由于 <code>LibA</code> 和 <code>LibB</code> 在 <code>MyProject</code> 的
                            <code>pom.xml</code>
                            中的声明顺序不影响依赖树的深度，Maven
                            的行为可能依赖于声明顺序，或者它可能会选择它先找到的那个。
                            这具有一定的不确定性。
                          </p>
                        </div>
                        <div class="strategy">
                          <h6>Gradle ("最新版本原则")</h6>
                          <p>
                            Gradle 会扫描整个依赖图，发现 <code>Guava</code> 有 v28.0 和 v30.0
                            两个版本被需要， 它会选择<strong>最高的版本</strong>，即 v30.0。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="strategy-comparison">
                    <h4>🔄 横向对比与关联</h4>
                    <div class="comparison-table">
                      <table>
                        <thead>
                          <tr>
                            <th>工具</th>
                            <th>默认策略</th>
                            <th>优点</th>
                            <th>缺点</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td><strong>Maven</strong></td>
                            <td>就近原则</td>
                            <td>算法简单，易于理解</td>
                            <td>结果可能不符合预期，依赖声明的顺序可能影响结果，脆弱</td>
                          </tr>
                          <tr>
                            <td><strong>Gradle</strong></td>
                            <td>最新版本原则</td>
                            <td>结果更符合直觉（通常新版本是更好的），更稳定</td>
                            <td>如果最新版不兼容，需要手动干预</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <div class="conflict-pitfalls">
                    <h4>⚠️ 常见理论误区与注意事项</h4>
                    <div class="pitfall-card">
                      <h5>❌ 误区</h5>
                      <p>
                        认为构建工具能自动解决所有冲突。它们只能根据其策略选择一个版本，
                        但不能保证这个版本是<strong>兼容</strong>的。开发者最终需要为兼容性负责。
                      </p>
                    </div>
                    <div class="pitfall-card">
                      <h5>⚠️ 注意</h5>
                      <p>
                        解决冲突的最终手段是<strong>强制指定一个版本</strong>，
                        但这要求你（开发者）明确知道这个版本能同时满足所有依赖方的需求。
                      </p>
                    </div>
                  </div>

                  <div class="conflict-real-world">
                    <h4>🏭 项目实践踩坑与解决方案</h4>
                    <div class="problem-solution">
                      <div class="problem-description">
                        <h5>🚨 常见问题描述</h5>
                        <p>
                          一个 Maven 项目引入了一个新的库 <code>LibC</code>，该库传递性地依赖了
                          <code>httpclient</code> 的一个非常旧的版本 4.1。
                          而项目中已经存在的另一个库 <code>LibD</code> 依赖的是
                          <code>httpclient</code> 的新版 4.5。 构建成功了，但在运行时，调用
                          <code>LibD</code> 的某个功能时，抛出了 <code>NoSuchMethodError</code>。
                        </p>
                      </div>
                      <div class="root-cause">
                        <h5>🔍 问题根源分析</h5>
                        <p>
                          <strong>Maven 的"就近原则"在这里做出了一个危险的选择</strong>。 假设
                          <code>LibC</code> 是项目的直接依赖，而
                          <code>LibD</code> 是一个更深层次的传递性依赖， Maven 就会选择更近的
                          <code>httpclient-4.1</code> 作为最终版本。 然而，<code>LibD</code>
                          的代码用到了只在 4.5 版本中才存在的一个新方法，从而导致了运行时错误。
                          <strong>Maven 在这种情况下优先保证了"能编译"，而不是"能正确运行"</strong
                          >。
                        </p>
                      </div>
                      <div class="solutions">
                        <h5>🛠️ 业界主流解决方案与权衡</h5>

                        <div class="solution-item maven-solution">
                          <h6>方案 A (Maven): 使用 &lt;dependencyManagement&gt; 强制版本</h6>
                          <p>
                            在父POM的
                            <code>&lt;dependencyManagement&gt;</code> 中明确声明项目要使用的
                            <code>httpclient</code> 版本是 4.5。
                            这会覆盖所有传递性依赖的版本，强制整个项目使用 4.5 版本。 这是 Maven
                            项目解决此类问题的<strong>最佳实践</strong>。
                          </p>
                        </div>

                        <div class="solution-item gradle-solution">
                          <h6>方案 B (Gradle): 使用 constraints 或 resolutionStrategy</h6>
                          <p>
                            Gradle 的默认策略（最新版本）在这种情况下会正确选择
                            4.5，通常不会出问题。
                            但如果需要更精细的控制，例如"我需要版本4.5，但绝不能是4.5.1，因为它有bug"，
                            可以使用 <code>constraints</code> 来声明版本约束。
                          </p>
                          <div class="code-example">
                            <pre><code>dependencies {
    constraints {
        implementation("org.apache.httpcomponents:httpclient") {
            version {
                require("4.5") // 要求最低是4.5
                reject("4.5.1")  // 拒绝有问题的版本
            }
            because("Version 4.5.1 has a critical bug")
        }
    }
}</code></pre>
                          </div>
                          <p>
                            Gradle 还有一个更强的武器 <code>resolutionStrategy.force</code>，
                            可以强制使用某个版本，但这通常是解决顽固冲突的最后手段。
                          </p>
                        </div>

                        <div class="pros-cons">
                          <div class="industry-choice">
                            <h6>🏭 权衡与业界选择</h6>
                            <p>
                              Gradle 的依赖解决机制被普遍认为比 Maven 更健壮、更安全。
                              它默认"快速失败"——如果发现严格的冲突（例如，一个要求<code
                                >[1.0, 2.0)</code
                              >， 另一个要求<code>[3.0, 4.0)</code
                              >），它会直接让构建失败，而不是选择一个可能错误的版本。
                              这迫使开发者尽早面对和解决问题。Maven
                              的方式则更容易将问题隐藏到运行时。 因此，在处理复杂依赖方面，<strong
                                >Gradle 的模型更受现代大型项目的青睐</strong
                              >。
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 思维导图部分 -->
                  <div class="chapter-mindmap">
                    <h4>🗺️ 第十一章全局回顾与思维导图</h4>
                    <div class="mindmap-intro">
                      <h5>📚 逻辑串联</h5>
                      <p>
                        第十一章是 Java 开发从"手工作坊"迈向"现代工业化生产"的必修课。
                        它首先阐明了<strong>构建工具的必要性</strong>，即自动化、依赖管理和保证一致性，
                        这是我们脱离刀耕火种时代的基础。接着，本章深入剖析了两种主流的"工业化方案"：
                        <strong>Maven</strong
                        >，它通过"约定优于配置"的哲学，为混乱的Java世界带来了秩序和标准； 以及
                        <strong>Gradle</strong>，它在 Maven 的基础上，通过"代码即配置"的理念，
                        赋予了构建过程无与伦比的<strong>灵活性和性能</strong>。
                        最后，通过对依赖冲突等核心痛点的<strong>实战对比</strong>，
                        我们清晰地看到了两者在设计哲学和解决问题能力上的差异，
                        从而能为自己的项目做出最明智的选择。
                      </p>
                    </div>
                    <div class="mindmap-container">
                      <div id="chapter11-final-mindmap" class="mermaid-container">
                        <div class="mindmap-placeholder">
                          <p>🎨 正在生成思维导图...</p>
                          <p>如果长时间未显示，请刷新页面</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>
          </main>
        </div>
      </div>
    </div>

    <!-- 返回顶部按钮 -->
    <BackToTopButton />

    <!-- 浮动章节菜单 -->
    <FloatingChapterMenu :chapters="chapterList" current-chapter="11" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import ExpandableSection from '@/components/ExpandableSection.vue'
import BackToTopButton from '@/components/BackToTopButton.vue'
import FloatingChapterMenu from '@/components/FloatingChapterMenu.vue'

// 响应式数据
const progress = ref(0)
const currentTopic = ref(0)
const showNotes = ref(false)
const showQuizModal = ref(false)

// 课程主题
const courseTopics = [
  {
    title: '构建工具的必要性',
    description: '理解为什么需要构建工具，三大核心职责',
  },
  {
    title: 'Maven：约定优于配置',
    description: '标准化项目结构与固定构建生命周期',
  },
  {
    title: 'Gradle：灵活性与性能',
    description: '通过代码实现灵活构建与性能优化',
  },
  {
    title: '实战对比与最佳实践',
    description: '依赖冲突处理与多模块项目管理',
  },
]

// 章节列表
const chapterList = [
  { number: 1, title: 'Java基础', path: '/chapter1' },
  { number: 2, title: 'Java模块系统', path: '/chapter2' },
  { number: 3, title: 'Java 17新特性', path: '/chapter3' },
  { number: 4, title: '类文件与字节码', path: '/chapter4' },
  { number: 5, title: 'Java并发基础', path: '/chapter5' },
  { number: 6, title: 'JDK并发库', path: '/chapter6' },
  { number: 7, title: 'Java性能优化', path: '/chapter7' },
  { number: 8, title: 'JVM替代语言', path: '/chapter8' },
  { number: 9, title: 'Kotlin', path: '/chapter9' },
  { number: 10, title: 'Clojure', path: '/chapter10' },
  { number: 11, title: '构建工具', path: '/chapter11' },
]

// 数据定义
const buildToolsNecessityData = {
  keyPoints: [
    '构建工具是将源代码转换成可执行软件并自动化相关任务的程序',
    '解决依赖地狱：自动管理几十上百个第三方库及其版本冲突',
    '消除重复易错工作流：自动化编译、测试、打包、部署等步骤',
    '保证环境一致性：通过wrapper和配置文件确保构建结果可复现',
    '核心原理：声明式配置、工作流自动化、一致性保证',
  ],
  interactiveElements: [
    { type: 'dependency-demo', label: '依赖管理演示' },
    { type: 'workflow-demo', label: '构建流程演示' },
    { type: 'wrapper-demo', label: 'Wrapper机制演示' },
  ],
}

const mavenConventionData = {
  keyPoints: [
    'Maven核心哲学：约定优于配置，通过标准化降低学习和维护成本',
    '项目对象模型(POM)：所有配置集中在pom.xml文件中的声明式描述',
    '标准目录结构：强制统一的目录布局，确保项目结构一致性',
    '构建生命周期：固定有序的构建阶段，从validate到deploy',
    '插件机制：通过插件目标绑定到生命周期阶段完成具体工作',
  ],
  interactiveElements: [
    { type: 'pom-demo', label: 'POM配置演示' },
    { type: 'lifecycle-demo', label: '生命周期演示' },
    { type: 'directory-demo', label: '目录结构演示' },
  ],
}

const gradleFlexibilityData = {
  keyPoints: [
    'Gradle核心优势：代码即配置的DSL，提供无限灵活性和强大性能',
    '任务图模型：基于有向无环图的任务依赖关系，智能执行顺序',
    '增量构建：通过输入/输出哈希值分析，跳过未改变的任务',
    '构建缓存：本地和远程缓存机制，实现数量级的构建速度提升',
    '解决Maven痛点：僵化的生命周期和缺乏缓存的性能问题',
  ],
  interactiveElements: [
    { type: 'gradle-dsl-demo', label: 'Gradle DSL演示' },
    { type: 'task-graph-demo', label: '任务图演示' },
    { type: 'incremental-build-demo', label: '增量构建演示' },
    { type: 'build-cache-demo', label: '构建缓存演示' },
  ],
}

const dependencyConflictData = {
  keyPoints: [
    '依赖冲突：同一库的多个版本要求，构建工具必须选择最终版本',
    'Maven就近原则：选择依赖树中距离最近的版本，可能导致运行时错误',
    'Gradle最新版本原则：选择最高版本，更符合直觉且更稳定',
    'Maven解决方案：使用dependencyManagement强制版本统一',
    'Gradle解决方案：使用constraints和resolutionStrategy精细控制',
  ],
  interactiveElements: [
    { type: 'conflict-demo', label: '依赖冲突演示' },
    { type: 'maven-resolution-demo', label: 'Maven解决策略演示' },
    { type: 'gradle-resolution-demo', label: 'Gradle解决策略演示' },
    { type: 'dependency-tree-demo', label: '依赖树分析演示' },
  ],
}

// 方法定义
const scrollToTopic = (index: number) => {
  const element = document.getElementById(`topic-${index}`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
    currentTopic.value = index
  }
}

const toggleNotes = () => {
  showNotes.value = !showNotes.value
}

const showQuiz = () => {
  showQuizModal.value = true
}

const handleInteraction = (type: string) => {
  console.log('Interaction:', type)
  // 处理交互逻辑
  switch (type) {
    case 'dependency-demo':
      // 依赖管理演示
      break
    case 'workflow-demo':
      // 构建流程演示
      break
    case 'wrapper-demo':
      // Wrapper机制演示
      break
    case 'pom-demo':
      // POM配置演示
      break
    case 'lifecycle-demo':
      // 生命周期演示
      break
    case 'directory-demo':
      // 目录结构演示
      break
    case 'gradle-dsl-demo':
      // Gradle DSL演示
      break
    case 'task-graph-demo':
      // 任务图演示
      break
    case 'incremental-build-demo':
      // 增量构建演示
      break
    case 'build-cache-demo':
      // 构建缓存演示
      break
    case 'conflict-demo':
      // 依赖冲突演示
      break
    case 'maven-resolution-demo':
      // Maven解决策略演示
      break
    case 'gradle-resolution-demo':
      // Gradle解决策略演示
      break
    case 'dependency-tree-demo':
      // 依赖树分析演示
      break
  }
}

// 渲染思维导图
const renderMindMap = async () => {
  try {
    const { default: mermaid } = await import('mermaid')

    mermaid.initialize({
      startOnLoad: false,
      theme: 'default',
      themeVariables: {
        primaryColor: '#667eea',
        primaryTextColor: '#fff',
        primaryBorderColor: '#764ba2',
        lineColor: '#667eea',
        secondaryColor: '#f8f9fa',
        tertiaryColor: '#e9ecef',
      },
    })

    // 构建工具思维导图内容
    const mindmapContent = `mindmap
  root((构建工具))
    必要性
      依赖地狱
        版本冲突
        传递依赖
        手动管理困难
      重复工作流
        编译
        测试
        打包
        部署
      环境不一致
        JDK版本
        依赖版本
        配置差异
    核心原理
      声明式配置
        依赖声明
        自动解析
        冲突处理
      工作流自动化
        生命周期
        任务定义
        插件机制
      一致性保证
        Wrapper机制
        配置文件
        版本锁定
    Maven特点
      约定优于配置
        标准目录
        固定生命周期
        插件绑定
      POM模型
        项目元数据
        依赖管理
        构建配置
      生命周期
        validate
        compile
        test
        package
        install
        deploy
    解决方案
      dependencyManagement
        版本统一
        BOM机制
        父子继承
      Wrapper机制
        版本一致
        自动下载
        零配置`

    // 完整的第十一章思维导图内容
    const finalMindmapContent = `mindmap
  root((第十一章 构建工具))
    (必要性)
      自动化 (编译, 测试, 打包)
      依赖管理 (传递性依赖, 仓库)
      一致性 (Wrapper)
    (Maven: 约定优于配置)
      核心: POM (pom.xml)
      固定生命周期 (Lifecycle)
      依赖解决: 就近原则
      实践: dependencyManagement
    (Gradle: 灵活性与性能)
      核心: DSL (build.gradle.kts)
      任务图 (Task-based DAG)
      依赖解决: 最新版本原则
      性能: 增量构建, 构建缓存
    (实战对比)
      依赖冲突处理
        Maven: dependency:tree, dependencyManagement
        Gradle: dependencies, dependencyInsight, constraints
      项目结构
        Maven: 僵化, 插件驱动
        Gradle: 灵活, 脚本驱动`

    const element = document.getElementById('build-tools-mindmap')
    if (element) {
      element.innerHTML = mindmapContent
      await mermaid.run({
        nodes: [element],
      })
    }

    // 渲染最终的完整思维导图
    const finalElement = document.getElementById('chapter11-final-mindmap')
    if (finalElement) {
      finalElement.innerHTML = finalMindmapContent
      await mermaid.run({
        nodes: [finalElement],
      })
    }
  } catch (error) {
    console.error('Failed to render mind map:', error)
    const element = document.getElementById('build-tools-mindmap')
    if (element) {
      element.innerHTML = '<p style="color: #dc3545;">思维导图加载失败，请刷新页面重试</p>'
    }
    const finalElement = document.getElementById('chapter11-final-mindmap')
    if (finalElement) {
      finalElement.innerHTML = '<p style="color: #dc3545;">思维导图加载失败，请刷新页面重试</p>'
    }
  }
}

// 滚动监听
const handleScroll = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const windowHeight = window.innerHeight
  const documentHeight = document.documentElement.scrollHeight

  // 计算进度
  progress.value = (scrollTop / (documentHeight - windowHeight)) * 100

  // 更新当前主题
  const topics = document.querySelectorAll('.topic-section')
  topics.forEach((topic, index) => {
    const rect = topic.getBoundingClientRect()
    if (rect.top <= windowHeight / 2 && rect.bottom >= windowHeight / 2) {
      currentTopic.value = index
    }
  })
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  // 渲染思维导图
  setTimeout(() => {
    renderMindMap()
  }, 1000)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.java-chapter11 {
  min-height: 100vh;
  background: #f8f9fa;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* 页面头部样式 */
.chapter-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem 0;
  position: relative;
  overflow: hidden;
}

.chapter-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
    repeat;
  opacity: 0.3;
}

.header-content {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 3rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

.chapter-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.chapter-subtitle {
  font-size: 1.5rem;
  opacity: 0.9;
  margin-bottom: 1rem;
  font-style: italic;
}

.chapter-description {
  font-size: 1.1rem;
  line-height: 1.6;
  opacity: 0.95;
  max-width: 600px;
}

/* 进度指示器 */
.progress-indicator {
  position: relative;
}

.progress-circle {
  position: relative;
  width: 120px;
  height: 120px;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-circle-fill {
  transition: stroke-dashoffset 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.2rem;
  font-weight: 600;
}

/* 内容布局 */
.content-wrapper {
  background: #f8f9fa;
  min-height: 100vh;
  padding: 2rem 0;
  overflow-x: hidden;
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  align-items: start;
  max-width: 100%;
  box-sizing: border-box;
}

/* 侧边栏样式 */
.sidebar {
  position: sticky;
  top: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.outline {
  padding: 1.5rem;
}

.outline h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.1rem;
}

.outline-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.outline-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.outline-item:hover {
  background: #f8f9fa;
  border-color: #e9ecef;
}

.outline-item.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: #667eea;
}

.outline-number {
  width: 24px;
  height: 24px;
  background: #e9ecef;
  color: #495057;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.outline-item.active .outline-number {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.outline-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.outline-content p {
  margin: 0;
  font-size: 0.8rem;
  opacity: 0.8;
  line-height: 1.3;
}

/* 工具栏 */
.toolbar {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 0.5rem;
}

.tool-button {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #dee2e6;
  background: white;
  border-radius: 6px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tool-button:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

/* 主内容区 */
.main-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.topic-section {
  border-bottom: 1px solid #e9ecef;
}

.topic-section:last-child {
  border-bottom: none;
}

/* 构建工具必要性样式 */
.build-tools-necessity-showcase {
  padding: 2rem;
}

.intro-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #495057;
  margin-bottom: 2rem;
}

.human-explanation {
  background: #e3f2fd;
  border-left: 4px solid #2196f3;
  padding: 1.5rem;
  margin: 2rem 0;
  border-radius: 0 8px 8px 0;
}

.human-explanation h4 {
  margin: 0 0 1rem 0;
  color: #1976d2;
}

.explanation-text {
  margin: 0;
  line-height: 1.6;
  color: #424242;
}

.core-problems {
  margin: 3rem 0;
}

.core-problems h4 {
  margin-bottom: 2rem;
  color: #333;
  font-size: 1.3rem;
}

.problems-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.problem-card {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.problem-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.problem-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.problem-icon {
  font-size: 1.5rem;
}

.problem-header h5 {
  margin: 0;
  color: #c53030;
  font-size: 1.1rem;
}

.problem-content p {
  margin: 0 0 1rem 0;
  line-height: 1.6;
  color: #4a5568;
}

.example {
  background: rgba(197, 48, 48, 0.1);
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #742a2a;
}

.core-principles {
  margin: 3rem 0;
}

.core-principles h4 {
  margin-bottom: 2rem;
  color: #333;
  font-size: 1.3rem;
}

.principles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.principle-card {
  background: #f0fff4;
  border: 1px solid #c6f6d5;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.principle-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.principle-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.principle-icon {
  font-size: 1.5rem;
}

.principle-header h5 {
  margin: 0;
  color: #2f855a;
  font-size: 1.1rem;
}

.principle-card p {
  margin: 0;
  line-height: 1.6;
  color: #4a5568;
}

/* Maven 样式 */
.maven-showcase {
  padding: 2rem;
}

.maven-core-concepts {
  margin: 3rem 0;
}

.maven-core-concepts h4 {
  margin-bottom: 2rem;
  color: #333;
  font-size: 1.3rem;
}

.concepts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.concept-card {
  background: #fefefe;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.concept-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.concept-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.concept-icon {
  font-size: 1.5rem;
  color: #667eea;
}

.concept-header h5 {
  margin: 0;
  color: #2d3748;
  font-size: 1.1rem;
}

.concept-content p {
  margin: 0 0 1rem 0;
  line-height: 1.6;
  color: #4a5568;
}

.code-example {
  background: #1a202c;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  font-family: 'Fira Code', monospace;
  font-size: 0.9rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.code-example pre {
  margin: 0;
  white-space: pre-wrap;
}

.directory-structure {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.dir-item {
  padding: 0.25rem 0;
  font-family: 'Fira Code', monospace;
  font-size: 0.9rem;
  color: #4a5568;
}

.lifecycle-flow {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin: 1rem 0;
}

.phase {
  background: #667eea;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.arrow {
  color: #667eea;
  font-weight: bold;
}

/* Maven 类比和演示样式 */
.maven-analogy {
  background: #fff8e1;
  border-left: 4px solid #ff9800;
  padding: 1.5rem;
  margin: 2rem 0;
  border-radius: 0 8px 8px 0;
}

.maven-analogy h4 {
  margin: 0 0 1rem 0;
  color: #e65100;
}

.analogy-content p {
  margin: 0 0 1rem 0;
  line-height: 1.6;
  color: #424242;
}

.analogy-list {
  margin: 0;
  padding-left: 1.5rem;
}

.analogy-list li {
  margin-bottom: 0.75rem;
  line-height: 1.6;
  color: #424242;
}

.maven-lifecycle-demo {
  margin: 3rem 0;
}

.maven-lifecycle-demo h4 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.3rem;
}

.lifecycle-explanation {
  background: #f3e5f5;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  border-left: 4px solid #9c27b0;
}

.lifecycle-explanation p {
  margin: 0;
  line-height: 1.6;
  color: #4a148c;
}

.lifecycle-demo {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.lifecycle-step {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: white;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  border: 2px solid #e0e0e0;
  min-width: 250px;
  transition: all 0.3s ease;
}

.lifecycle-step.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #e0e0e0;
  color: #424242;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.lifecycle-step.active .step-number {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.step-content h6 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.step-content p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.8;
}

.step-arrow {
  font-size: 1.5rem;
  color: #667eea;
  font-weight: bold;
}

/* Maven 误区样式 */
.maven-pitfalls {
  margin: 3rem 0;
}

.maven-pitfalls h4 {
  margin-bottom: 2rem;
  color: #333;
  font-size: 1.3rem;
}

.pitfalls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.pitfall-card {
  background: #fff3e0;
  border: 1px solid #ffcc02;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.pitfall-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.pitfall-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.pitfall-icon {
  font-size: 1.5rem;
}

.pitfall-header h5 {
  margin: 0;
  color: #e65100;
  font-size: 1.1rem;
}

.pitfall-card p {
  margin: 0;
  line-height: 1.6;
  color: #4a5568;
}

/* 实际问题解决方案样式 */
.real-world-problems {
  margin: 4rem 0 2rem 0;
  padding-top: 2rem;
  border-top: 2px solid #e9ecef;
}

.real-world-problems h3 {
  margin-bottom: 2rem;
  color: #dc3545;
  font-size: 1.5rem;
}

.problem-solution-card {
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.problem-section,
.root-cause-section,
.solution-section {
  padding: 2rem;
  border-bottom: 1px solid #dee2e6;
}

.solution-section {
  border-bottom: none;
}

.problem-section {
  background: #fff5f5;
  border-left: 4px solid #dc3545;
}

.root-cause-section {
  background: #fff8e1;
  border-left: 4px solid #ff9800;
}

.solution-section {
  background: #f0fff4;
  border-left: 4px solid #28a745;
}

.problem-section h4,
.root-cause-section h4,
.solution-section h4 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.problem-section h4 {
  color: #dc3545;
}

.root-cause-section h4 {
  color: #e65100;
}

.solution-section h4 {
  color: #28a745;
}

.solution-demo h5 {
  margin: 0 0 1rem 0;
  color: #155724;
  font-size: 1.1rem;
}

.solution-steps {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.solution-steps li {
  margin-bottom: 0.75rem;
  line-height: 1.6;
  color: #424242;
}

.code-examples {
  margin: 2rem 0;
}

.code-example-section {
  margin-bottom: 1.5rem;
}

.code-example-section h6 {
  margin: 0 0 0.5rem 0;
  color: #495057;
  font-size: 1rem;
  font-weight: 600;
}

.tradeoffs {
  margin: 2rem 0;
}

.tradeoffs h5 {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.1rem;
}

.tradeoffs-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.pros,
.cons {
  padding: 1rem;
  border-radius: 8px;
}

.pros {
  background: #d4edda;
  border: 1px solid #c3e6cb;
}

.cons {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
}

.pros h6 {
  margin: 0 0 0.75rem 0;
  color: #155724;
}

.cons h6 {
  margin: 0 0 0.75rem 0;
  color: #721c24;
}

.pros ul,
.cons ul {
  margin: 0;
  padding-left: 1.5rem;
}

.pros li,
.cons li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.pros li {
  color: #155724;
}

.cons li {
  color: #721c24;
}

.industry-choice {
  background: #e3f2fd;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.industry-choice p {
  margin: 0;
  line-height: 1.6;
  color: #0d47a1;
}

/* 思维导图样式 */
.build-tools-mindmap {
  margin: 3rem 0;
}

.build-tools-mindmap h4 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.3rem;
}

.mindmap-container {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.mermaid-container {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mindmap-placeholder {
  text-align: center;
  color: #666;
}

.mindmap-placeholder p {
  margin: 0.5rem 0;
}

/* Wrapper解决方案样式 */
.wrapper-solution {
  margin: 3rem 0;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 2rem;
  border-left: 4px solid #28a745;
}

.wrapper-solution h4 {
  margin: 0 0 1.5rem 0;
  color: #155724;
  font-size: 1.3rem;
}

.wrapper-explanation {
  margin-bottom: 2rem;
}

.wrapper-explanation p {
  margin: 0;
  line-height: 1.6;
  color: #495057;
}

.wrapper-mechanism {
  margin-bottom: 2rem;
}

.wrapper-mechanism h5 {
  margin: 0 0 1rem 0;
  color: #155724;
  font-size: 1.1rem;
}

.mechanism-steps {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.step-number {
  width: 28px;
  height: 28px;
  background: #28a745;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  font-weight: 600;
  flex-shrink: 0;
}

.step p {
  margin: 0;
  line-height: 1.5;
  color: #495057;
}

.wrapper-benefits h5 {
  margin: 0 0 1rem 0;
  color: #155724;
  font-size: 1.1rem;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #c3e6cb;
}

.benefit-icon {
  font-size: 1.2rem;
  color: #28a745;
  flex-shrink: 0;
}

.benefit-item h6 {
  margin: 0 0 0.25rem 0;
  color: #155724;
  font-size: 0.95rem;
}

.benefit-item p {
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.4;
  color: #495057;
}

/* Gradle 样式 */
.gradle-showcase {
  padding: 2rem;
}

.gradle-principles {
  margin: 3rem 0;
}

.gradle-principles .principles-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1.5rem;
}

.gradle-principles .principle-card {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.gradle-principles .principle-card h5 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
}

.gradle-principles .principle-card ul {
  margin: 0;
  padding-left: 1.5rem;
}

.gradle-principles .principle-card li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.gradle-example {
  margin: 3rem 0;
}

.code-example {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1rem 0;
}

.code-example h5 {
  margin: 0 0 1rem 0;
  color: #333;
}

.code-example pre {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  overflow-x: auto;
  margin: 1rem 0;
}

.code-example code {
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
}

.code-note {
  margin: 1rem 0 0 0;
  font-style: italic;
  color: #666;
}

.analogy {
  background: #e3f2fd;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
  margin: 1rem 0;
}

.analogy h5 {
  margin: 0 0 1rem 0;
  color: #1976d2;
}

.gradle-comparison {
  margin: 3rem 0;
}

.comparison-table {
  overflow-x: auto;
  margin: 1.5rem 0;
}

.comparison-table table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.comparison-table th,
.comparison-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.comparison-table th {
  background: #667eea;
  color: white;
  font-weight: 600;
}

.comparison-table tr:last-child td {
  border-bottom: none;
}

.comparison-table tr:nth-child(even) {
  background: #f8f9fa;
}

.gradle-pitfalls {
  margin: 3rem 0;
}

.pitfall-card {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1rem 0;
}

.pitfall-card h5 {
  margin: 0 0 1rem 0;
  color: #856404;
}

.gradle-real-world {
  margin: 3rem 0;
}

.problem-solution {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 2rem;
  margin: 1.5rem 0;
}

.problem-description,
.root-cause,
.solutions {
  margin: 2rem 0;
}

.problem-description h5,
.root-cause h5,
.solutions h5 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
}

.solution-item {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1rem 0;
  border-left: 4px solid #28a745;
}

.solution-item h6 {
  margin: 0 0 1rem 0;
  color: #155724;
  font-size: 1rem;
}

.solution-details {
  margin: 1rem 0;
}

.feature {
  margin: 1.5rem 0;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.feature strong {
  color: #333;
  display: block;
  margin-bottom: 0.5rem;
}

.pros-cons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin: 1.5rem 0;
}

.pros,
.cons {
  padding: 1rem;
  border-radius: 8px;
}

.pros {
  background: #d4edda;
  border-left: 4px solid #28a745;
}

.cons {
  background: #f8d7da;
  border-left: 4px solid #dc3545;
}

.pros h6,
.cons h6 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
}

.pros ul,
.cons ul {
  margin: 0;
  padding-left: 1.5rem;
}

.pros li {
  color: #155724;
}

.cons li {
  color: #721c24;
}

.industry-choice {
  background: #e3f2fd;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.industry-choice p {
  margin: 0;
  line-height: 1.6;
  color: #0d47a1;
}

/* 依赖冲突样式 */
.dependency-conflict-showcase {
  padding: 2rem;
}

.conflict-principles {
  margin: 3rem 0;
}

.conflict-principles .principles-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1.5rem;
}

.conflict-example {
  margin: 3rem 0;
}

.example-scenario {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1.5rem 0;
}

.example-scenario h5 {
  margin: 0 0 1rem 0;
  color: #333;
}

.dependency-tree {
  background: white;
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
}

.dependency-tree ul {
  margin: 0;
  padding-left: 1.5rem;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
}

.dependency-tree li {
  margin: 0.5rem 0;
  color: #495057;
}

.resolution-strategies {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin: 1.5rem 0;
}

.strategy {
  background: white;
  border-radius: 6px;
  padding: 1rem;
  border-left: 4px solid #667eea;
}

.strategy h6 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 0.9rem;
}

.strategy p {
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.4;
  color: #666;
}

.strategy-comparison {
  margin: 3rem 0;
}

.conflict-pitfalls {
  margin: 3rem 0;
}

.conflict-real-world {
  margin: 3rem 0;
}

.maven-solution {
  border-left-color: #dc3545;
}

.gradle-solution {
  border-left-color: #28a745;
}

.maven-solution h6 {
  color: #721c24;
}

.gradle-solution h6 {
  color: #155724;
}

.chapter-mindmap {
  margin: 4rem 0 2rem 0;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.chapter-mindmap h4 {
  margin: 0 0 2rem 0;
  color: #333;
  font-size: 1.4rem;
  text-align: center;
}

.mindmap-intro {
  margin-bottom: 2rem;
}

.mindmap-intro h5 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
}

.mindmap-intro p {
  margin: 0;
  line-height: 1.6;
  color: #555;
  text-align: justify;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .sidebar {
    position: static;
    order: -1;
  }

  .header-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .chapter-title {
    font-size: 2.5rem;
  }

  .gradle-principles .principles-grid,
  .conflict-principles .principles-grid {
    grid-template-columns: 1fr;
  }

  .resolution-strategies {
    grid-template-columns: 1fr;
  }

  .pros-cons {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .chapter-title {
    font-size: 2rem;
  }

  .problems-grid,
  .principles-grid,
  .concepts-grid,
  .pitfalls-grid,
  .gradle-principles .principles-grid,
  .conflict-principles .principles-grid,
  .resolution-strategies,
  .pros-cons {
    grid-template-columns: 1fr;
  }

  .comparison-table {
    font-size: 0.8rem;
  }

  .comparison-table th,
  .comparison-table td {
    padding: 0.5rem;
  }

  .lifecycle-flow {
    flex-direction: column;
    align-items: stretch;
  }

  .arrow {
    transform: rotate(90deg);
    align-self: center;
  }

  .tradeoffs-grid {
    grid-template-columns: 1fr;
  }

  .lifecycle-step {
    min-width: auto;
  }

  .step-arrow {
    transform: rotate(90deg);
  }

  .code-examples {
    margin: 1rem 0;
  }

  .solution-steps {
    padding-left: 1rem;
  }

  .problem-solution-card .problem-section,
  .problem-solution-card .root-cause-section,
  .problem-solution-card .solution-section {
    padding: 1.5rem;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
  }

  .mechanism-steps {
    gap: 0.75rem;
  }

  .step {
    padding: 0.75rem;
  }

  .mindmap-container {
    padding: 1rem;
  }

  .wrapper-solution {
    padding: 1.5rem;
  }
}
</style>
